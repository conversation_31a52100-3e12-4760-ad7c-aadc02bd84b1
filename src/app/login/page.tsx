import { LoginForm } from '@/components/auth/login-form';
import { auth } from '@/lib/auth';
import { redirect } from 'next/navigation';

export default async function LoginPage() {
	// Nếu đã đăng nhập, chuyển hướng về trang chủ
	const session = await auth();
	if (session?.user) {
		redirect('/');
	}

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-md w-full space-y-8">
				<div className="text-center">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						Vocab Learning
					</h1>
					<p className="text-gray-600">
						Chào mừng bạn đến với ứng dụng học từ vựng
					</p>
				</div>
				<LoginForm />
			</div>
		</div>
	);
}