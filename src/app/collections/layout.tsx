import { ReactNode } from 'react';
import CollectionsLayoutClient from './collections-layout-client';
import { getCollectionsApi } from '@/backend/api';

export default async function CollectionsLayout({ children }: { children: ReactNode }) {
	const initialCollections = await getCollectionsApi();
	return (
		<CollectionsLayoutClient initialCollections={initialCollections}>
			{children}
		</CollectionsLayoutClient>
	);
}
