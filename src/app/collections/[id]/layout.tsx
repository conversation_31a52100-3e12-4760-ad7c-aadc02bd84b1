import { notFound } from 'next/navigation';
import { ReactNode } from 'react';
import { CollectionDetailLayoutClient } from './collection-detail-layout-client';
import { getCollectionApi } from '@/backend/api';

export default async function CollectionDetailLayout({
	children,
	params,
}: {
	children: ReactNode;
	params: Promise<{ id: string }>;
}) {
	const { id } = await params;
	const initialCollection = await getCollectionApi(id);
	if (!initialCollection) notFound();

	return (
		initialCollection && (
			<CollectionDetailLayoutClient initialCollection={initialCollection}>
				{children}
			</CollectionDetailLayoutClient>
		)
	);
}
