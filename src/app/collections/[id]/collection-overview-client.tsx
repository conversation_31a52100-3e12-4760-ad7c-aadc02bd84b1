'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, Translate } from '@/components/ui';
import { ErrorDisplay } from '@/components/ui/error-display';
import { FeatureCard } from '@/components/ui/feature-card';
import { useCollections } from '@/hooks';
import { getTranslationKeyOfLanguage } from '@/lib';
import { CollectionWithDetail } from '@/models';
import {
	BookOpen,
	Calendar,
	CheckCircle2,
	Clock,
	Edit3,
	FileText,
	Globe,
	GraduationCap,
	Hash,
	ListChecks,
	MessageSquare,
	RefreshCw,
	Target,
	TrendingUp,
	Zap,
	Sparkles,
	ArrowRight,
} from 'lucide-react';

function CollectionInfoCard({ collection }: { collection: CollectionWithDetail }) {
	return (
		<div className="relative mb-8 overflow-hidden">
			{/* Background decoration */}
			<div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-secondary/5 dark:from-primary/10 dark:via-primary/20 dark:to-secondary/10 rounded-2xl" />
			<div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/20 to-transparent rounded-full blur-2xl" />
			<div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-secondary/20 to-transparent rounded-full blur-xl" />

			<Card className="relative border-primary/20 bg-background/80 backdrop-blur-sm shadow-lg">
				<CardHeader className="pb-4">
					<CardTitle className="text-3xl font-bold flex items-center gap-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
						<div className="p-2 rounded-xl bg-primary/10 border border-primary/20">
							<BookOpen className="h-8 w-8 text-primary" />
						</div>
						<Translate
							text="collections.overview.welcome_title"
							values={{ name: collection.name }}
						/>
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
						<div className="group flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-blue-50 to-blue-100/50 dark:from-blue-950/30 dark:to-blue-900/20 border border-blue-200/50 dark:border-blue-800/30 transition-all duration-200 hover:shadow-md">
							<div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
								<Globe className="h-5 w-5 text-blue-600 dark:text-blue-400" />
							</div>
							<div className="flex-1">
								<div className="text-xs font-medium text-blue-600 dark:text-blue-400 uppercase tracking-wide">
									<Translate text="languages.source_language" />
								</div>
								<div className="text-sm font-semibold text-blue-900 dark:text-blue-100">
									<Translate
										text={getTranslationKeyOfLanguage(
											collection.source_language
										)}
									/>
								</div>
							</div>
						</div>

						<div className="group flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-950/30 dark:to-green-900/20 border border-green-200/50 dark:border-green-800/30 transition-all duration-200 hover:shadow-md">
							<div className="p-2 rounded-lg bg-green-500/10 border border-green-500/20">
								<Globe className="h-5 w-5 text-green-600 dark:text-green-400" />
							</div>
							<div className="flex-1">
								<div className="text-xs font-medium text-green-600 dark:text-green-400 uppercase tracking-wide">
									<Translate text="languages.target_language" />
								</div>
								<div className="text-sm font-semibold text-green-900 dark:text-green-100">
									<Translate
										text={getTranslationKeyOfLanguage(
											collection.target_language
										)}
									/>
								</div>
							</div>
						</div>

						<div className="group flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-purple-50 to-purple-100/50 dark:from-purple-950/30 dark:to-purple-900/20 border border-purple-200/50 dark:border-purple-800/30 transition-all duration-200 hover:shadow-md">
							<div className="p-2 rounded-lg bg-purple-500/10 border border-purple-500/20">
								<Hash className="h-5 w-5 text-purple-600 dark:text-purple-400" />
							</div>
							<div className="flex-1">
								<div className="text-xs font-medium text-purple-600 dark:text-purple-400 uppercase tracking-wide">
									<Translate text="collections.overview.word_count" />
								</div>
								<div className="text-lg font-bold text-purple-900 dark:text-purple-100">
									{collection.word_ids.length.toLocaleString()}
								</div>
							</div>
						</div>

						<div className="group flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-orange-50 to-orange-100/50 dark:from-orange-950/30 dark:to-orange-900/20 border border-orange-200/50 dark:border-orange-800/30 transition-all duration-200 hover:shadow-md">
							<div className="p-2 rounded-lg bg-orange-500/10 border border-orange-500/20">
								<Calendar className="h-5 w-5 text-orange-600 dark:text-orange-400" />
							</div>
							<div className="flex-1">
								<div className="text-xs font-medium text-orange-600 dark:text-orange-400 uppercase tracking-wide">
									<Translate text="collections.overview.created_at" />
								</div>
								<div className="text-sm font-semibold text-orange-900 dark:text-orange-100">
									{new Date(collection.created_at).toLocaleDateString()}
								</div>
							</div>
						</div>

						<div className="group flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-teal-50 to-teal-100/50 dark:from-teal-950/30 dark:to-teal-900/20 border border-teal-200/50 dark:border-teal-800/30 transition-all duration-200 hover:shadow-md">
							<div className="p-2 rounded-lg bg-teal-500/10 border border-teal-500/20">
								<Clock className="h-5 w-5 text-teal-600 dark:text-teal-400" />
							</div>
							<div className="flex-1">
								<div className="text-xs font-medium text-teal-600 dark:text-teal-400 uppercase tracking-wide">
									<Translate text="collections.overview.updated_at" />
								</div>
								<div className="text-sm font-semibold text-teal-900 dark:text-teal-100">
									{new Date(collection.updated_at).toLocaleDateString()}
								</div>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}

function CollectionOverviewSkeleton() {
	return (
		<div className="container mx-auto py-8 space-y-8">
			{/* Collection Info Card Skeleton */}
			<div className="relative overflow-hidden">
				<div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-muted/50 to-muted/30 rounded-2xl animate-pulse" />
				<Card className="relative border-muted/50 bg-background/80 backdrop-blur-sm">
					<CardHeader className="pb-4">
						<div className="flex items-center gap-4">
							<div className="p-2 rounded-xl bg-muted/50 animate-pulse">
								<div className="h-8 w-8 bg-muted rounded" />
							</div>
							<div className="h-8 w-80 bg-muted rounded-lg animate-pulse" />
						</div>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
							{[...Array(5)].map((_, i) => (
								<div
									key={i}
									className="p-4 rounded-xl bg-muted/30 border border-muted/50 animate-pulse"
								>
									<div className="flex items-center gap-3">
										<div className="p-2 rounded-lg bg-muted/50">
											<div className="h-5 w-5 bg-muted rounded" />
										</div>
										<div className="flex-1 space-y-2">
											<div className="h-3 w-20 bg-muted rounded" />
											<div className="h-4 w-24 bg-muted rounded" />
										</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Feature Cards Skeleton */}
			<div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
				{[...Array(3)].map((_, i) => (
					<Card
						key={i}
						className="h-80 border-muted/50 bg-gradient-to-br from-background to-muted/20 animate-pulse"
					>
						<CardHeader className="pb-4">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									<div className="p-2 rounded-xl bg-muted/50">
										<div className="h-6 w-6 bg-muted rounded" />
									</div>
									<div className="h-6 w-32 bg-muted rounded" />
								</div>
								<div className="h-4 w-4 bg-muted rounded" />
							</div>
							<div className="h-4 w-48 bg-muted rounded mt-3" />
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{[...Array(4)].map((_, j) => (
									<div
										key={j}
										className="flex items-center gap-3 p-3 rounded-lg bg-muted/30"
									>
										<div className="h-4 w-4 bg-muted rounded" />
										<div className="h-4 w-32 bg-muted rounded" />
										<div className="ml-auto h-4 w-4 bg-muted rounded" />
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}

export function CollectionOverviewClient() {
	const { currentCollection, loading, error, setError } = useCollections();

	const features = currentCollection
		? [
				{
					titleKey: 'collections.tabs.vocabulary',
					descriptionKey: 'collections.overview.vocabulary_desc',
					icon: GraduationCap,
					link: `/collections/${currentCollection.id}/vocabulary`,
					subFeatures: [
						{
							titleKey: 'collections.tabs.generate_words',
							icon: Zap,
							subLink: '/generate',
						},
						{
							titleKey: 'collections.tabs.my_words_list',
							icon: ListChecks,
							subLink: '/my-words',
						},
						{
							titleKey: 'collections.tabs.review',
							icon: RefreshCw,
							subLink: '/review',
						},
						{
							titleKey: 'collections.tabs.multiple_choice_practice',
							icon: Target,
							subLink: '/mcq',
						},
					],
				},
				{
					titleKey: 'collections.tabs.paragraphs',
					descriptionKey: 'collections.overview.paragraphs_desc',
					icon: FileText,
					link: `/collections/${currentCollection.id}/paragraph`,
					subFeatures: [
						{
							titleKey: 'collections.tabs.paragraph_practice',
							icon: Edit3,
							subLink: '/paragraph-practice',
						},
						{
							titleKey: 'qa_practice.tab_title',
							icon: MessageSquare,
							subLink: '/qa-practice',
						},
						{
							titleKey: 'collections.tabs.grammar_practice',
							icon: CheckCircle2,
							subLink: '/grammar-practice',
						},
					],
				},
				{
					titleKey: 'collections.stats.title',
					descriptionKey: 'collections.stats.description',
					icon: TrendingUp,
					link: `/collections/${currentCollection.id}/stats`,
					subFeatures: [],
				},
		  ]
		: [];

	// Show loading skeleton while collection is being set or loaded
	if (loading.setCurrent || loading.get || !currentCollection) {
		return <CollectionOverviewSkeleton />;
	}

	return (
		<>
			<ErrorDisplay error={error} onDismiss={() => setError(null)} />
			<div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
				<div className="container mx-auto py-8 space-y-8">
					{/* Hero Section */}
					<div className="text-center space-y-4 mb-12">
						<div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-primary font-medium text-sm">
							<Sparkles className="h-4 w-4" />
							<Translate text="collections.overview.welcome_back" />
						</div>
						<h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-foreground via-foreground to-foreground/70 bg-clip-text text-transparent">
							<Translate text="collections.overview.dashboard_title" />
						</h1>
						<p className="text-lg text-muted-foreground max-w-2xl mx-auto">
							<Translate text="collections.overview.dashboard_subtitle" />
						</p>
					</div>

					<CollectionInfoCard collection={currentCollection} />

					{/* Features Section */}
					<div className="space-y-6">
						<div className="flex items-center gap-3">
							<h2 className="text-2xl font-bold">
								<Translate text="collections.overview.features_title" />
							</h2>
							<div className="h-px flex-1 bg-gradient-to-r from-border to-transparent" />
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
							{features.map((feature, index) => (
								<div
									key={feature.titleKey}
									className="group animate-in fade-in-0 slide-in-from-bottom-4"
									style={{ animationDelay: `${index * 100}ms` }}
								>
									<FeatureCard
										titleKey={feature.titleKey}
										descriptionKey={feature.descriptionKey}
										icon={feature.icon}
										link={feature.link}
										subFeatures={feature.subFeatures}
									/>
								</div>
							))}
						</div>
					</div>

					{/* Quick Actions */}
					<div className="mt-16 p-8 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-secondary/5 border border-primary/20">
						<div className="text-center space-y-4">
							<h3 className="text-xl font-semibold">
								<Translate text="collections.overview.quick_start_title" />
							</h3>
							<p className="text-muted-foreground">
								<Translate text="collections.overview.quick_start_desc" />
							</p>
							<div className="flex flex-wrap justify-center gap-4 mt-6">
								<a
									href={`/collections/${currentCollection.id}/vocabulary/generate`}
									className="inline-flex items-center gap-2 px-6 py-3 rounded-xl bg-primary text-primary-foreground font-medium hover:bg-primary/90 transition-colors group"
								>
									<Zap className="h-4 w-4" />
									<Translate text="collections.tabs.generate_words" />
									<ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
								</a>
								<a
									href={`/collections/${currentCollection.id}/vocabulary/review`}
									className="inline-flex items-center gap-2 px-6 py-3 rounded-xl bg-secondary text-secondary-foreground font-medium hover:bg-secondary/80 transition-colors group"
								>
									<RefreshCw className="h-4 w-4" />
									<Translate text="collections.tabs.review" />
									<ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
