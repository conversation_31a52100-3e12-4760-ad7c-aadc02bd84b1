'use client';

import {
	CollectionsSection,
	FeedbackSection,
	GamificationSection,
	HeroSection,
	LearningPathSection,
	ParagraphsSection,
	PracticeTypesSection,
	ReadingWritingSection,
	cardVariants,
	containerVariants,
} from '@/components/home';
import { Button, ScreenReaderAnnouncement, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { motion } from 'framer-motion';
import { ArrowRight, BookOpen, Brain, Sparkles, Target, Users, Zap } from 'lucide-react';
import Link from 'next/link';

export default function Home() {
	const { t } = useTranslation();

	return (
		<motion.main
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			transition={{ duration: 0.5 }}
			role="main"
			aria-label="Home page"
		>
			<ScreenReaderAnnouncement message={t('home.title')} priority="polite" />

			<div className="fixed inset-0 -z-10 bg-gradient-to-br from-primary/20 via-background to-secondary/20 animate-gradient-x transition-colors duration-300" />

			<motion.div
				variants={containerVariants}
				initial="hidden"
				animate="visible"
				className="space-y-16 text-center"
			>
				{/* Hero Section */}
				<HeroSection />

				{/* Quick Start CTA */}
				<motion.div variants={cardVariants} className="space-y-6">
					<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
						<Link href="/collections">
							<Button
								size="lg"
								className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300 text-lg px-8 py-6 h-auto"
							>
								<BookOpen className="size-5 mr-2" />
								<Translate text="home.get_started" />
								<ArrowRight className="size-5 ml-2" />
							</Button>
						</Link>
						<span className="text-muted-foreground">
							<Translate text="home.or" />
						</span>
						<Link href="#features">
							<Button
								variant="outline"
								size="lg"
								className="text-lg px-8 py-6 h-auto"
							>
								<Target className="size-5 mr-2" />
								<Translate text="home.explore_features" />
							</Button>
						</Link>
					</div>
				</motion.div>

				{/* Key Features Highlight */}
				<motion.div variants={cardVariants} className="space-y-8" id="features">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<motion.div
							whileHover={{ scale: 1.05 }}
							className="p-6 bg-gradient-to-br from-blue-500/10 to-blue-600/5 rounded-2xl border border-blue-500/20"
						>
							<Brain className="size-12 text-blue-500 mx-auto mb-4" />
							<h3 className="text-xl font-semibold mb-2">
								<Translate text="home.ai_powered" />
							</h3>
							<p className="text-muted-foreground">
								<Translate text="home.ai_powered_description" />
							</p>
						</motion.div>

						<motion.div
							whileHover={{ scale: 1.05 }}
							className="p-6 bg-gradient-to-br from-green-500/10 to-green-600/5 rounded-2xl border border-green-500/20"
						>
							<Zap className="size-12 text-green-500 mx-auto mb-4" />
							<h3 className="text-xl font-semibold mb-2">
								<Translate text="home.smart_practice" />
							</h3>
							<p className="text-muted-foreground">
								<Translate text="home.smart_practice_description" />
							</p>
						</motion.div>

						<motion.div
							whileHover={{ scale: 1.05 }}
							className="p-6 bg-gradient-to-br from-purple-500/10 to-purple-600/5 rounded-2xl border border-purple-500/20"
						>
							<Users className="size-12 text-purple-500 mx-auto mb-4" />
							<h3 className="text-xl font-semibold mb-2">
								<Translate text="home.multilingual" />
							</h3>
							<p className="text-muted-foreground">
								<Translate text="home.multilingual_description" />
							</p>
						</motion.div>
					</div>
				</motion.div>

				{/* Collections Section - Primary Feature */}
				<CollectionsSection />

				{/* Divider */}
				<div className="my-8 h-1 w-1/3 mx-auto bg-gradient-to-r from-primary via-secondary to-primary rounded-full opacity-60" />

				{/* Practice Types */}
				<motion.div variants={cardVariants} className="space-y-8">
					<PracticeTypesSection />
				</motion.div>

				{/* Paragraphs Section - AI-powered content */}
				<motion.div variants={cardVariants} className="space-y-8">
					<ParagraphsSection />
				</motion.div>

				{/* Reading & Writing */}
				<motion.div variants={cardVariants} className="space-y-8">
					<ReadingWritingSection />
				</motion.div>

				{/* Gamification */}
				<motion.div variants={cardVariants} className="space-y-8">
					<GamificationSection />
				</motion.div>

				{/* Learning Path */}
				<motion.div variants={cardVariants} className="space-y-8">
					<LearningPathSection />
				</motion.div>

				{/* Statistics Section */}
				<motion.div variants={cardVariants} className="space-y-8">
					<div className="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 border border-primary/10">
						<h2 className="text-2xl font-bold mb-6">
							<Sparkles className="size-6 inline mr-2 text-primary" />
							<Translate text="home.why_choose_vocab" />
						</h2>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
							<div>
								<div className="text-3xl font-bold text-primary mb-2">AI</div>
								<p className="text-muted-foreground">
									<Translate text="home.powered_by_ai" />
								</p>
							</div>
							<div>
								<div className="text-3xl font-bold text-primary mb-2">2</div>
								<p className="text-muted-foreground">
									<Translate text="home.languages_supported" />
								</p>
							</div>
							<div>
								<div className="text-3xl font-bold text-primary mb-2">∞</div>
								<p className="text-muted-foreground">
									<Translate text="home.unlimited_practice" />
								</p>
							</div>
						</div>
					</div>
				</motion.div>

				{/* Feedback Section */}
				<motion.div variants={cardVariants} className="space-y-8">
					<FeedbackSection />
				</motion.div>

				{/* Final CTA */}
				<motion.div variants={cardVariants} className="space-y-6 pb-16">
					<h2 className="text-3xl font-bold">
						<Translate text="home.ready_to_start" />
					</h2>
					<p className="text-muted-foreground text-lg max-w-2xl mx-auto">
						<Translate text="home.join_thousands" />
					</p>
					<Link href="/collections">
						<Button
							size="lg"
							className="bg-primary hover:bg-primary/90 transition-all duration-300 text-lg px-12 py-6 h-auto"
						>
							<BookOpen className="size-5 mr-2" />
							<Translate text="home.start_learning_now" />
							<ArrowRight className="size-5 ml-2" />
						</Button>
					</Link>
				</motion.div>
			</motion.div>
		</motion.main>
	);
}
