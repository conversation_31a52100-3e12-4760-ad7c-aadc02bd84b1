# Regional Variations Development Plan

## Overview
Implement support for regional language variations to provide learners with authentic, location-specific vocabulary and expressions. This feature will help users understand how language differs across regions and cultures.

## Technical Architecture

### Database Schema Extensions
```prisma
model RegionalVariation {
  id          String   @id @default(uuid())
  word_id     String
  region      Region
  variation   String
  usage_notes String?
  frequency   Float    @default(1.0)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  
  word        Word     @relation(fields: [word_id], references: [id])
  
  @@unique([word_id, region])
  @@index([region])
  @@index([word_id])
}

enum Region {
  US_ENGLISH
  UK_ENGLISH
  AUSTRALIAN_ENGLISH
  CANADIAN_ENGLISH
  SOUTH_AFRICAN_ENGLISH
  NORTHERN_VIETNAM
  SOUTHERN_VIETNAM
  CENTRAL_VIETNAM
}

model RegionalPreference {
  id         String   @id @default(uuid())
  user_id    String
  region     Region
  priority   Int      @default(1)
  created_at DateTime @default(now())
  
  user       User     @relation(fields: [user_id], references: [id])
  
  @@unique([user_id, region])
  @@index([user_id])
}
```

### Service Layer Implementation

#### Regional Variation Service
```typescript
interface RegionalVariationService {
  getVariationsByWord(wordId: string, regions?: Region[]): Promise<RegionalVariation[]>
  addVariation(wordId: string, region: Region, variation: string, usageNotes?: string): Promise<RegionalVariation>
  updateVariation(variationId: string, updates: Partial<RegionalVariation>): Promise<RegionalVariation>
  deleteVariation(variationId: string): Promise<void>
  getUserPreferredVariations(userId: string, wordId: string): Promise<RegionalVariation[]>
  setUserRegionalPreferences(userId: string, preferences: RegionalPreference[]): Promise<void>
  getRegionalStatistics(region: Region): Promise<RegionalStatistics>
}

interface RegionalStatistics {
  region: Region
  totalVariations: number
  mostCommonWords: string[]
  uniqueExpressions: number
  coveragePercentage: number
}
```

#### Word Service Extensions
```typescript
interface WordWithRegionalVariations extends Word {
  regionalVariations: RegionalVariation[]
  preferredVariation?: RegionalVariation
}

// Extend existing WordService
interface WordService {
  // ... existing methods
  getWordWithRegionalVariations(wordId: string, userId?: string): Promise<WordWithRegionalVariations>
  searchWordsWithRegionalContext(query: string, regions: Region[]): Promise<WordWithRegionalVariations[]>
}
```

### Frontend Components

#### Regional Variation Display Component
```typescript
interface RegionalVariationDisplayProps {
  word: WordWithRegionalVariations
  selectedRegions: Region[]
  onRegionToggle: (region: Region) => void
  showUsageNotes?: boolean
}

export function RegionalVariationDisplay({
  word,
  selectedRegions,
  onRegionToggle,
  showUsageNotes = true
}: RegionalVariationDisplayProps) {
  // Component implementation
}
```

#### Regional Preference Settings
```typescript
interface RegionalPreferenceSettingsProps {
  userId: string
  currentPreferences: RegionalPreference[]
  onPreferencesUpdate: (preferences: RegionalPreference[]) => void
}

export function RegionalPreferenceSettings({
  userId,
  currentPreferences,
  onPreferencesUpdate
}: RegionalPreferenceSettingsProps) {
  // Component implementation
}
```

### API Endpoints

#### Regional Variation API
```typescript
// GET /api/words/[wordId]/regional-variations
export async function getWordRegionalVariationsApi(wordId: string, regions?: Region[]) {
  // Implementation
}

// POST /api/words/[wordId]/regional-variations
export async function addRegionalVariationApi(
  wordId: string,
  variation: CreateRegionalVariationRequest
) {
  // Implementation
}

// PUT /api/regional-variations/[variationId]
export async function updateRegionalVariationApi(
  variationId: string,
  updates: UpdateRegionalVariationRequest
) {
  // Implementation
}

// DELETE /api/regional-variations/[variationId]
export async function deleteRegionalVariationApi(variationId: string) {
  // Implementation
}
```

#### User Regional Preferences API
```typescript
// GET /api/users/regional-preferences
export async function getUserRegionalPreferencesApi() {
  // Implementation
}

// PUT /api/users/regional-preferences
export async function updateUserRegionalPreferencesApi(
  preferences: RegionalPreference[]
) {
  // Implementation
}
```

## Implementation Phases

### Phase 1: Core Infrastructure (2-3 weeks)
1. **Database Schema Setup**
   - Create RegionalVariation and RegionalPreference models
   - Add migration scripts
   - Set up indexes for performance

2. **Repository Layer**
   - Implement RegionalVariationRepository
   - Extend UserRepository for preferences
   - Add query methods for regional data

3. **Service Layer Foundation**
   - Create RegionalVariationService
   - Extend WordService with regional methods
   - Implement basic CRUD operations

### Phase 2: API Development (2 weeks)
1. **API Endpoints**
   - Regional variation CRUD endpoints
   - User preference management endpoints
   - Regional statistics endpoints

2. **Validation & Error Handling**
   - Input validation for regional data
   - Custom error types for regional operations
   - API documentation

### Phase 3: Frontend Integration (3 weeks)
1. **Core Components**
   - RegionalVariationDisplay component
   - RegionalPreferenceSettings component
   - Region selector components

2. **User Interface**
   - Regional variation indicators in word cards
   - Settings page for regional preferences
   - Regional context in search results

3. **User Experience**
   - Smooth transitions between regional views
   - Tooltips and help text for regional features
   - Responsive design for mobile devices

### Phase 4: Data Population & Testing (2 weeks)
1. **Data Collection**
   - Research and compile regional variations
   - Create data import scripts
   - Validate data accuracy

2. **Testing & Quality Assurance**
   - Unit tests for all services
   - Integration tests for API endpoints
   - User acceptance testing

## Data Sources & Content Strategy

### English Variations
- American vs British spelling differences
- Regional slang and colloquialisms
- Professional terminology variations
- Cultural context differences

### Vietnamese Variations
- Northern, Central, Southern dialect differences
- Regional vocabulary preferences
- Formal vs informal usage patterns
- Historical language evolution

### Content Curation Process
1. **Expert Review**: Linguists validate regional variations
2. **Community Input**: Users can suggest regional variations
3. **Automated Detection**: ML models identify potential variations
4. **Quality Control**: Regular audits ensure accuracy

## Performance Considerations

### Caching Strategy
- Cache regional variations by word and region
- User preference caching for faster lookups
- Regional statistics caching with TTL

### Database Optimization
- Proper indexing on region and word_id fields
- Query optimization for regional searches
- Pagination for large regional datasets

### Frontend Performance
- Lazy loading of regional variations
- Efficient state management for region preferences
- Optimized rendering of variation displays

## Analytics & Metrics

### User Engagement Metrics
- Regional preference adoption rates
- Variation view frequency
- User feedback on regional accuracy

### Content Quality Metrics
- Variation coverage per region
- User-reported accuracy issues
- Expert validation scores

### Performance Metrics
- API response times for regional queries
- Database query performance
- Frontend rendering performance

## Future Enhancements

### Advanced Features
- Audio pronunciations for regional variations
- Regional usage frequency indicators
- Historical evolution of regional differences
- Cross-regional comparison tools

### Machine Learning Integration
- Automatic regional variation detection
- Personalized regional recommendations
- Regional usage pattern analysis
- Predictive regional preference modeling

## Success Criteria

### Technical Success
- Sub-200ms response times for regional queries
- 99.9% uptime for regional features
- Zero data loss during regional operations

### User Success
- 70% of users engage with regional features
- 85% user satisfaction with regional accuracy
- 50% increase in vocabulary retention with regional context

### Content Success
- 80% coverage of common words with regional variations
- 95% accuracy rating from expert reviewers
- Active community contribution to regional content
