# Predictive Memory Insights - Development Plan

## Overview
Implement an advanced system that predicts memory retention, identifies optimal review timing, and provides insights into memory formation patterns to enhance long-term learning outcomes.

## Technical Architecture

### Core Components

#### 1. Memory Prediction Engine
- **Location**: `src/backend/services/memory-prediction.service.ts`
- **Purpose**: Predict memory retention and forgetting curves
- **Models**:
  - Ebbinghaus forgetting curve variants
  - Spaced repetition optimization
  - Memory consolidation modeling
  - Interference prediction

#### 2. Memory Analytics Service
- **Location**: `src/backend/services/memory-analytics.service.ts`
- **Purpose**: Analyze memory formation and retention patterns
- **Features**:
  - Memory strength assessment
  - Consolidation tracking
  - Interference detection
  - Retrieval practice optimization

#### 3. Retention Forecasting System
- **Location**: `src/backend/services/retention-forecasting.service.ts`
- **Purpose**: Forecast long-term retention outcomes
- **Capabilities**:
  - Multi-timeframe predictions
  - Confidence intervals
  - Risk assessment
  - Intervention recommendations

## Database Schema Extensions

### New Tables

```prisma
model MemoryTrace {
  id                  String   @id @default(uuid())
  user_id             String
  content_id          String
  content_type        String   // 'word', 'concept', 'fact'
  initial_strength    Float    @default(0.0) // 0.0-1.0
  current_strength    Float    @default(0.0) // 0.0-1.0
  decay_rate          Float    @default(0.1) // Forgetting rate
  consolidation_level Float    @default(0.0) // How well consolidated
  interference_score  Float    @default(0.0) // Interference from other memories
  last_retrieval      DateTime?
  retrieval_count     Int      @default(0)
  successful_retrievals Int    @default(0)
  created_at          DateTime @default(now())
  updated_at          DateTime @updatedAt
  
  user User @relation(fields: [user_id], references: [id])
  
  @@unique([user_id, content_id, content_type])
  @@index([user_id])
  @@index([current_strength])
  @@index([last_retrieval])
}

model MemoryPrediction {
  id                String   @id @default(uuid())
  user_id           String
  content_id        String
  content_type      String
  prediction_type   String   // 'retention', 'forgetting', 'optimal_review'
  predicted_value   Float    // Predicted retention probability
  confidence_interval Json   // [lower_bound, upper_bound]
  prediction_horizon Int     // Days into future
  factors_considered Json    // Factors used in prediction
  created_at        DateTime @default(now())
  target_date       DateTime
  actual_outcome    Float?   // Actual retention when measured
  prediction_accuracy Float? // How accurate the prediction was
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([prediction_type])
  @@index([target_date])
}

model MemoryConsolidation {
  id                    String   @id @default(uuid())
  user_id               String
  content_id            String
  content_type          String
  consolidation_phase   String   // 'encoding', 'early', 'late', 'stable'
  consolidation_strength Float   // 0.0-1.0
  sleep_quality_impact  Float?   // Impact of sleep on consolidation
  spacing_effect_score  Float    // Benefit from spaced repetition
  elaboration_score     Float    // Benefit from elaborative processing
  context_variability   Float    // Benefit from varied contexts
  measured_at           DateTime @default(now())
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([consolidation_phase])
  @@index([measured_at])
}

model InterferencePattern {
  id                String   @id @default(uuid())
  user_id           String
  source_content_id String   // Content causing interference
  target_content_id String   // Content being interfered with
  interference_type String   // 'proactive', 'retroactive', 'similarity'
  interference_strength Float // 0.0-1.0
  similarity_score  Float    // How similar the contents are
  temporal_proximity Int     // How close in time they were learned
  resolution_strategy String? // How to resolve the interference
  detected_at       DateTime @default(now())
  resolved_at       DateTime?
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([interference_type])
  @@index([detected_at])
}

model OptimalReviewSchedule {
  id              String   @id @default(uuid())
  user_id         String
  content_id      String
  content_type    String
  next_review     DateTime
  review_interval Int      // Days until next review
  difficulty_boost Float   // Adjustment based on difficulty
  retention_target Float   // Target retention probability
  confidence_score Float   // Confidence in schedule
  schedule_version String  @default("1.0")
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  user User @relation(fields: [user_id], references: [id])
  
  @@unique([user_id, content_id, content_type])
  @@index([user_id])
  @@index([next_review])
}

model MemoryInsight {
  id              String   @id @default(uuid())
  user_id         String
  insight_type    String   // 'retention_risk', 'optimal_timing', 'interference_warning'
  title           String
  description     String
  affected_content Json    // List of content IDs affected
  recommended_action String
  urgency_level   String   // 'low', 'medium', 'high', 'critical'
  confidence      Float    // 0.0-1.0
  created_at      DateTime @default(now())
  acknowledged_at DateTime?
  action_taken    Boolean  @default(false)
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([insight_type])
  @@index([urgency_level])
  @@index([created_at])
}
```

## Implementation Plan

### Phase 1: Memory Modeling Foundation (Week 1-2)

#### 1.1 Forgetting Curve Implementation
```typescript
// src/backend/services/memory-prediction.service.ts
export interface MemoryPredictionService {
  predictRetention(
    userId: string,
    contentId: string,
    timeHorizon: number
  ): Promise<RetentionPrediction>;
  
  calculateOptimalReviewTime(
    userId: string,
    contentId: string,
    targetRetention: number
  ): Promise<OptimalReviewTime>;
  
  updateMemoryStrength(
    userId: string,
    contentId: string,
    retrievalSuccess: boolean,
    retrievalDifficulty: number
  ): Promise<MemoryTrace>;
  
  detectMemoryInterference(
    userId: string,
    newContentId: string
  ): Promise<InterferencePattern[]>;
}

interface ForgettingCurveModel {
  calculateRetention(
    initialStrength: number,
    timeElapsed: number,
    decayRate: number
  ): number;
  
  calculateDecayRate(
    retrievalHistory: RetrievalEvent[]
  ): number;
  
  adjustForSpacingEffect(
    baseRetention: number,
    spacingPattern: SpacingPattern
  ): number;
}
```

#### 1.2 Enhanced Forgetting Curve Models
```typescript
class EbbinghausForgettingCurve implements ForgettingCurveModel {
  // R(t) = e^(-t/S)
  calculateRetention(
    initialStrength: number,
    timeElapsed: number,
    decayRate: number
  ): number {
    const stabilityFactor = 1 / decayRate;
    return initialStrength * Math.exp(-timeElapsed / stabilityFactor);
  }
  
  calculateDecayRate(retrievalHistory: RetrievalEvent[]): number {
    if (retrievalHistory.length < 2) return 0.1; // Default decay rate
    
    // Calculate decay rate based on retrieval success pattern
    const successRate = retrievalHistory.filter(r => r.success).length / retrievalHistory.length;
    const avgInterval = this.calculateAverageInterval(retrievalHistory);
    
    // Higher success rate and longer intervals = slower decay
    return Math.max(0.01, 0.2 * (1 - successRate) * (1 / Math.log(avgInterval + 1)));
  }
}

class PowerLawForgetting implements ForgettingCurveModel {
  // R(t) = (1 + t/α)^(-β)
  calculateRetention(
    initialStrength: number,
    timeElapsed: number,
    decayRate: number
  ): number {
    const alpha = 1.0; // Scale parameter
    const beta = decayRate; // Shape parameter
    
    return initialStrength * Math.pow(1 + timeElapsed / alpha, -beta);
  }
}

class AdaptiveForgettingCurve implements ForgettingCurveModel {
  calculateRetention(
    initialStrength: number,
    timeElapsed: number,
    decayRate: number,
    personalFactors?: PersonalFactors
  ): number {
    let baseRetention = Math.exp(-timeElapsed * decayRate);
    
    if (personalFactors) {
      // Adjust for personal factors
      baseRetention *= personalFactors.memoryCapacityMultiplier;
      baseRetention *= personalFactors.sleepQualityMultiplier;
      baseRetention *= personalFactors.stressLevelMultiplier;
    }
    
    return Math.min(1.0, Math.max(0.0, initialStrength * baseRetention));
  }
}
```

### Phase 2: Memory Analytics Engine (Week 3-4)

#### 2.1 Memory Strength Assessment
```typescript
// src/backend/services/memory-analytics.service.ts
export interface MemoryAnalyticsService {
  assessMemoryStrength(
    userId: string,
    contentId: string
  ): Promise<MemoryStrengthAssessment>;
  
  analyzeConsolidationProgress(
    userId: string,
    contentId: string
  ): Promise<ConsolidationAnalysis>;
  
  detectInterferencePatterns(
    userId: string
  ): Promise<InterferencePattern[]>;
  
  generateMemoryInsights(
    userId: string
  ): Promise<MemoryInsight[]>;
}

class MemoryStrengthCalculator {
  async calculateMemoryStrength(
    retrievalHistory: RetrievalEvent[],
    timingData: TimingData,
    contextFactors: ContextFactors
  ): Promise<MemoryStrengthAssessment> {
    // Base strength from retrieval success
    const retrievalStrength = this.calculateRetrievalStrength(retrievalHistory);
    
    // Adjustment for timing (spacing effect)
    const timingAdjustment = this.calculateTimingAdjustment(timingData);
    
    // Adjustment for context variability
    const contextAdjustment = this.calculateContextAdjustment(contextFactors);
    
    // Consolidation level
    const consolidationLevel = this.assessConsolidation(retrievalHistory, timingData);
    
    const overallStrength = (
      retrievalStrength * 0.4 +
      timingAdjustment * 0.3 +
      contextAdjustment * 0.2 +
      consolidationLevel * 0.1
    );
    
    return {
      overallStrength,
      retrievalStrength,
      consolidationLevel,
      confidence: this.calculateConfidence(retrievalHistory.length),
      factors: {
        timing: timingAdjustment,
        context: contextAdjustment,
        consolidation: consolidationLevel
      }
    };
  }
  
  private calculateRetrievalStrength(history: RetrievalEvent[]): number {
    if (history.length === 0) return 0;
    
    // Weight recent retrievals more heavily
    let weightedSum = 0;
    let totalWeight = 0;
    
    history.forEach((event, index) => {
      const recencyWeight = Math.exp(-0.1 * (history.length - index - 1));
      const successValue = event.success ? 1 : 0;
      const difficultyAdjustment = 1 - event.difficulty * 0.3;
      
      weightedSum += successValue * difficultyAdjustment * recencyWeight;
      totalWeight += recencyWeight;
    });
    
    return weightedSum / totalWeight;
  }
}
```

#### 2.2 Interference Detection
```typescript
class InterferenceDetector {
  async detectInterference(
    userId: string,
    targetContent: ContentItem,
    recentContent: ContentItem[]
  ): Promise<InterferencePattern[]> {
    const interferences: InterferencePattern[] = [];
    
    for (const sourceContent of recentContent) {
      const similarity = await this.calculateSimilarity(targetContent, sourceContent);
      
      if (similarity > 0.7) { // High similarity threshold
        const interferenceStrength = this.calculateInterferenceStrength(
          similarity,
          targetContent,
          sourceContent
        );
        
        if (interferenceStrength > 0.3) {
          interferences.push({
            sourceContentId: sourceContent.id,
            targetContentId: targetContent.id,
            interferenceType: this.determineInterferenceType(targetContent, sourceContent),
            interferenceStrength,
            similarityScore: similarity,
            temporalProximity: this.calculateTemporalProximity(targetContent, sourceContent),
            resolutionStrategy: this.suggestResolutionStrategy(similarity, interferenceStrength)
          });
        }
      }
    }
    
    return interferences;
  }
  
  private async calculateSimilarity(
    content1: ContentItem,
    content2: ContentItem
  ): Promise<number> {
    // Use semantic similarity for text content
    if (content1.type === 'word' && content2.type === 'word') {
      return await this.calculateSemanticSimilarity(content1.text, content2.text);
    }
    
    // Use feature similarity for other content types
    return this.calculateFeatureSimilarity(content1.features, content2.features);
  }
  
  private suggestResolutionStrategy(
    similarity: number,
    interferenceStrength: number
  ): string {
    if (similarity > 0.9 && interferenceStrength > 0.7) {
      return 'discriminative_practice'; // Practice distinguishing between similar items
    } else if (interferenceStrength > 0.5) {
      return 'spaced_interleaving'; // Interleave practice of similar items
    } else {
      return 'context_variation'; // Practice in different contexts
    }
  }
}
```

### Phase 3: Retention Forecasting (Week 5-6)

#### 3.1 Multi-timeframe Predictions
```typescript
// src/backend/services/retention-forecasting.service.ts
export interface RetentionForecastingService {
  forecastRetention(
    userId: string,
    contentIds: string[],
    timeHorizons: number[]
  ): Promise<RetentionForecast>;
  
  predictOptimalSchedule(
    userId: string,
    contentIds: string[],
    targetRetention: number
  ): Promise<OptimalSchedule>;
  
  assessRetentionRisk(
    userId: string,
    timeframe: number
  ): Promise<RetentionRiskAssessment>;
}

class RetentionForecaster {
  async forecastRetention(
    memoryTraces: MemoryTrace[],
    timeHorizons: number[]
  ): Promise<RetentionForecast> {
    const forecasts: TimeframeForecast[] = [];
    
    for (const horizon of timeHorizons) {
      const predictions: ContentPrediction[] = [];
      
      for (const trace of memoryTraces) {
        const prediction = await this.predictSingleContent(trace, horizon);
        predictions.push(prediction);
      }
      
      forecasts.push({
        timeHorizon: horizon,
        predictions,
        overallRetention: this.calculateOverallRetention(predictions),
        confidence: this.calculateForecastConfidence(predictions)
      });
    }
    
    return {
      forecasts,
      riskFactors: await this.identifyRiskFactors(memoryTraces),
      recommendations: this.generateRecommendations(forecasts)
    };
  }
  
  private async predictSingleContent(
    trace: MemoryTrace,
    timeHorizon: number
  ): Promise<ContentPrediction> {
    const currentTime = Date.now();
    const timeSinceLastRetrieval = trace.last_retrieval 
      ? (currentTime - trace.last_retrieval.getTime()) / (1000 * 60 * 60 * 24)
      : timeHorizon;
    
    const totalTimeElapsed = timeSinceLastRetrieval + timeHorizon;
    
    // Use adaptive forgetting curve
    const forgettingCurve = new AdaptiveForgettingCurve();
    const predictedRetention = forgettingCurve.calculateRetention(
      trace.current_strength,
      totalTimeElapsed,
      trace.decay_rate
    );
    
    // Adjust for interference
    const interferenceAdjustment = 1 - trace.interference_score * 0.3;
    const finalPrediction = predictedRetention * interferenceAdjustment;
    
    return {
      contentId: trace.content_id,
      predictedRetention: finalPrediction,
      confidence: this.calculatePredictionConfidence(trace),
      riskLevel: this.assessRiskLevel(finalPrediction),
      recommendedAction: this.recommendAction(finalPrediction, trace)
    };
  }
}
```

### Phase 4: Intelligent Review Scheduling (Week 7-8)

#### 4.1 Optimal Review Timing
```typescript
class OptimalScheduler {
  async calculateOptimalSchedule(
    memoryTraces: MemoryTrace[],
    targetRetention: number = 0.85,
    constraints: SchedulingConstraints
  ): Promise<OptimalSchedule> {
    const scheduleItems: ScheduleItem[] = [];
    
    for (const trace of memoryTraces) {
      const optimalTime = await this.findOptimalReviewTime(trace, targetRetention);
      
      scheduleItems.push({
        contentId: trace.content_id,
        nextReview: optimalTime,
        interval: this.calculateInterval(trace.last_retrieval, optimalTime),
        priority: this.calculatePriority(trace, optimalTime),
        estimatedDuration: this.estimateReviewDuration(trace)
      });
    }
    
    // Optimize schedule considering constraints
    const optimizedSchedule = this.optimizeSchedule(scheduleItems, constraints);
    
    return {
      items: optimizedSchedule,
      totalEstimatedTime: this.calculateTotalTime(optimizedSchedule),
      expectedRetention: this.calculateExpectedRetention(optimizedSchedule),
      efficiency: this.calculateEfficiency(optimizedSchedule)
    };
  }
  
  private async findOptimalReviewTime(
    trace: MemoryTrace,
    targetRetention: number
  ): Promise<Date> {
    const forgettingCurve = new AdaptiveForgettingCurve();
    
    // Binary search for optimal time
    let minTime = 1; // 1 day
    let maxTime = 365; // 1 year
    let optimalTime = minTime;
    
    while (maxTime - minTime > 0.1) {
      const midTime = (minTime + maxTime) / 2;
      const predictedRetention = forgettingCurve.calculateRetention(
        trace.current_strength,
        midTime,
        trace.decay_rate
      );
      
      if (predictedRetention >= targetRetention) {
        optimalTime = midTime;
        minTime = midTime;
      } else {
        maxTime = midTime;
      }
    }
    
    const now = new Date();
    return new Date(now.getTime() + optimalTime * 24 * 60 * 60 * 1000);
  }
  
  private optimizeSchedule(
    items: ScheduleItem[],
    constraints: SchedulingConstraints
  ): ScheduleItem[] {
    // Use genetic algorithm for schedule optimization
    const population = this.generateInitialPopulation(items, constraints);
    
    for (let generation = 0; generation < 50; generation++) {
      const fitness = this.evaluatePopulation(population, constraints);
      const newPopulation = this.evolvePopulation(population, fitness);
      population.splice(0, population.length, ...newPopulation);
    }
    
    return this.selectBestSchedule(population);
  }
}
```

## Frontend Integration

### Memory Insights Dashboard
```typescript
// src/components/ui/memory-insights-dashboard.tsx
export function MemoryInsightsDashboard({ userId }: { userId: string }) {
  const { insights, predictions, loading } = useMemoryInsights(userId);
  const { schedule } = useOptimalSchedule(userId);
  
  return (
    <div className="memory-insights-dashboard">
      <div className="dashboard-header">
        <h2>Memory Insights</h2>
        <MemoryHealthIndicator insights={insights} />
      </div>
      
      <div className="insights-grid">
        <RetentionForecastChart predictions={predictions} />
        <MemoryStrengthHeatmap userId={userId} />
        <InterferenceWarnings insights={insights.filter(i => i.insight_type === 'interference_warning')} />
        <OptimalScheduleCalendar schedule={schedule} />
      </div>
      
      <div className="actionable-insights">
        <h3>Recommended Actions</h3>
        <InsightActionsList insights={insights} />
      </div>
    </div>
  );
}
```

### Custom Hooks
```typescript
// src/hooks/use-memory-insights.ts
export function useMemoryInsights(userId: string) {
  const [insights, setInsights] = useState<MemoryInsight[]>([]);
  const [predictions, setPredictions] = useState<RetentionForecast | null>(null);
  const [loading, setLoading] = useState(true);
  
  const refreshInsights = useCallback(async () => {
    setLoading(true);
    try {
      const [newInsights, newPredictions] = await Promise.all([
        getMemoryInsightsApi(userId),
        getRetentionForecastApi(userId, [1, 7, 30, 90])
      ]);
      
      setInsights(newInsights);
      setPredictions(newPredictions);
    } finally {
      setLoading(false);
    }
  }, [userId]);
  
  useEffect(() => {
    refreshInsights();
  }, [refreshInsights]);
  
  return {
    insights,
    predictions,
    loading,
    refreshInsights
  };
}
```

## Success Criteria

### Prediction Accuracy
- 90%+ accuracy for 1-day retention predictions
- 85%+ accuracy for 1-week retention predictions
- 75%+ accuracy for 1-month retention predictions
- 65%+ accuracy for 3-month retention predictions

### User Experience Impact
- 40% improvement in long-term retention
- 30% reduction in unnecessary reviews
- 25% increase in study efficiency
- 50% better adherence to review schedules

## Timeline

- **Week 1-2**: Memory modeling foundation and forgetting curves
- **Week 3-4**: Memory analytics and interference detection
- **Week 5-6**: Retention forecasting and risk assessment
- **Week 7-8**: Optimal scheduling and frontend integration
- **Week 9**: Testing and validation
- **Week 10**: Deployment and monitoring
